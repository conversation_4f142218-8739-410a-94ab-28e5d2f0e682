<?php
session_start();
require_once 'config.php';

// Simulate a processing session
$processing_id = 'test_' . uniqid();
$output_path = OUTPUT_DIR . $processing_id . '/';

// Create output directory
if (!file_exists($output_path)) {
    mkdir($output_path, 0755, true);
}

// Create session data
$_SESSION['processing'][$processing_id] = [
    'original_name' => 'test_audio.mp3',
    'file_path' => 'uploads/test_audio.mp3',
    'status' => 'completed',
    'progress' => 100,
    'message' => 'Processing completed successfully!',
    'created_at' => time(),
    'output_files' => [
        'vocals' => $output_path . 'vocals.wav',
        'bass' => $output_path . 'bass.wav',
        'drums' => $output_path . 'drums.wav',
        'other' => $output_path . 'other.wav'
    ]
];

// Create dummy WAV files
function createDummyWavFile($filepath, $track_type) {
    // Create a minimal WAV file header for demonstration
    $sample_rate = 44100;
    $duration = 10; // 10 seconds
    $channels = 2; // stereo
    $bits_per_sample = 16;
    
    $data_size = $sample_rate * $duration * $channels * ($bits_per_sample / 8);
    $file_size = $data_size + 36;
    
    // WAV header
    $header = pack('V', 0x46464952); // "RIFF"
    $header .= pack('V', $file_size);
    $header .= pack('V', 0x45564157); // "WAVE"
    $header .= pack('V', 0x20746d66); // "fmt "
    $header .= pack('V', 16); // PCM header size
    $header .= pack('v', 1); // PCM format
    $header .= pack('v', $channels);
    $header .= pack('V', $sample_rate);
    $header .= pack('V', $sample_rate * $channels * ($bits_per_sample / 8));
    $header .= pack('v', $channels * ($bits_per_sample / 8));
    $header .= pack('v', $bits_per_sample);
    $header .= pack('V', 0x61746164); // "data"
    $header .= pack('V', $data_size);
    
    // Create file with header and some dummy audio data
    $file = fopen($filepath, 'wb');
    fwrite($file, $header);
    
    // Generate some dummy audio data
    for ($i = 0; $i < $data_size / 4; $i++) {
        $sample = 0;
        switch ($track_type) {
            case 'vocals':
                $sample = ($i % 8000 < 100) ? 5000 : 0;
                break;
            case 'bass':
                $sample = ($i % 16000 < 200) ? 3000 : 0;
                break;
            case 'drums':
                $sample = ($i % 4000 < 50) ? 8000 : 0;
                break;
            case 'other':
                $sample = ($i % 12000 < 150) ? 4000 : 0;
                break;
        }
        
        fwrite($file, pack('v', $sample)); // Left channel
        fwrite($file, pack('v', $sample)); // Right channel
    }
    
    fclose($file);
}

// Create the dummy files
$tracks = ['vocals', 'bass', 'drums', 'other'];
foreach ($tracks as $track) {
    createDummyWavFile($output_path . $track . '.wav', $track);
}

echo "<h2>Test Processing Created</h2>";
echo "<p><strong>Processing ID:</strong> $processing_id</p>";
echo "<p><strong>Status:</strong> " . $_SESSION['processing'][$processing_id]['status'] . "</p>";

echo "<h3>Test Download Links:</h3>";
foreach ($tracks as $track) {
    $file_path = $output_path . $track . '.wav';
    $exists = file_exists($file_path) ? 'EXISTS' : 'NOT FOUND';
    $size = file_exists($file_path) ? filesize($file_path) : 0;
    echo "<p><a href='download.php?id=$processing_id&track=$track' target='_blank'>Download $track</a> ($exists, $size bytes)</p>";
}
echo "<p><a href='download.php?id=$processing_id&all=true' target='_blank'>Download All Tracks (ZIP)</a></p>";

echo "<h3>Go to Process Page:</h3>";
echo "<p><a href='process.php?id=$processing_id' target='_blank'>View Process Page</a></p>";

// Save session
session_write_close();
?>
