<?php
session_start();
require_once 'config.php';

echo "<h1>🔧 Installation Test Results</h1>";

echo "<div style='font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif; margin: 20px; background: #f5f5f5; padding: 20px; border-radius: 10px;'>";

// Test Python
echo "<h2>🐍 Python Installation</h2>";
$python_path = "$env:LOCALAPPDATA\\Programs\\Python\\Python311\\python.exe";
$python_test = shell_exec("\"$python_path\" --version 2>&1");
if ($python_test && strpos($python_test, 'Python') !== false) {
    echo "<p style='color: green;'>✅ <strong>Python:</strong> " . trim($python_test) . "</p>";
    
    // Test TensorFlow
    $tf_test = shell_exec("\"$python_path\" -c \"import tensorflow as tf; print('TensorFlow', tf.__version__)\" 2>&1");
    if ($tf_test && strpos($tf_test, 'TensorFlow') !== false) {
        echo "<p style='color: green;'>✅ <strong>TensorFlow:</strong> " . trim($tf_test) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ <strong>TensorFlow:</strong> Not working</p>";
        echo "<pre style='background: #fff; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($tf_test) . "</pre>";
    }
    
    // Test Spleeter
    $spleeter_test = shell_exec("\"$python_path\" -c \"import spleeter; print('Spleeter available')\" 2>&1");
    if ($spleeter_test && strpos($spleeter_test, 'Spleeter available') !== false) {
        echo "<p style='color: green;'>✅ <strong>Spleeter:</strong> Available</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ <strong>Spleeter:</strong> Not available (compatibility issues with Python 3.11)</p>";
    }
} else {
    echo "<p style='color: red;'>❌ <strong>Python:</strong> Not found</p>";
}

// Test FFmpeg
echo "<h2>🎬 FFmpeg Installation</h2>";
$ffmpeg_test = shell_exec("ffmpeg -version 2>&1");
if ($ffmpeg_test && strpos($ffmpeg_test, 'ffmpeg version') !== false) {
    $version_line = explode("\n", $ffmpeg_test)[0];
    echo "<p style='color: green;'>✅ <strong>FFmpeg:</strong> " . htmlspecialchars($version_line) . "</p>";
} else {
    echo "<p style='color: orange;'>⚠️ <strong>FFmpeg:</strong> Installed but PATH not updated (restart terminal needed)</p>";
    
    // Try alternative paths
    $alt_paths = [
        'C:\\ffmpeg\\bin\\ffmpeg.exe',
        'C:\\Program Files\\ffmpeg\\bin\\ffmpeg.exe',
        'C:\\tools\\ffmpeg\\bin\\ffmpeg.exe'
    ];
    
    foreach ($alt_paths as $path) {
        if (file_exists($path)) {
            $alt_test = shell_exec("\"$path\" -version 2>&1");
            if ($alt_test && strpos($alt_test, 'ffmpeg version') !== false) {
                $version_line = explode("\n", $alt_test)[0];
                echo "<p style='color: green;'>✅ <strong>FFmpeg (alternative path):</strong> " . htmlspecialchars($version_line) . "</p>";
                break;
            }
        }
    }
}

// Current application status
echo "<h2>🎵 Application Status</h2>";
$has_python = $python_test && strpos($python_test, 'Python') !== false;
$has_tensorflow = $tf_test && strpos($tf_test, 'TensorFlow') !== false;
$has_ffmpeg = $ffmpeg_test && strpos($ffmpeg_test, 'ffmpeg version') !== false;

if ($has_tensorflow) {
    echo "<p style='color: green;'>🎯 <strong>Status:</strong> Ready for AI-based processing (with custom implementation)</p>";
    echo "<p>✅ TensorFlow is available for building custom separation models</p>";
} elseif ($has_ffmpeg) {
    echo "<p style='color: green;'>🔧 <strong>Status:</strong> Ready for FFmpeg-based processing</p>";
    echo "<p>✅ Can perform basic audio separation using frequency filtering</p>";
} else {
    echo "<p style='color: orange;'>🎭 <strong>Status:</strong> Demo mode only</p>";
    echo "<p>⚠️ Install FFmpeg or restart terminal to enable real audio processing</p>";
}

// Recommendations
echo "<h2>💡 Recommendations</h2>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 10px; margin: 10px 0;'>";

if (!$has_ffmpeg) {
    echo "<h3>🔧 For Basic Audio Separation:</h3>";
    echo "<ol>";
    echo "<li><strong>Restart your terminal/command prompt</strong> (FFmpeg was just installed)</li>";
    echo "<li>Or restart your computer to update PATH</li>";
    echo "<li>Test with: <code>ffmpeg -version</code></li>";
    echo "</ol>";
}

if ($has_tensorflow) {
    echo "<h3>🎯 For Advanced AI Separation:</h3>";
    echo "<ol>";
    echo "<li>TensorFlow is ready for custom model implementation</li>";
    echo "<li>Consider using pre-trained models or APIs</li>";
    echo "<li>Alternative: Use online services like LALAL.AI API</li>";
    echo "</ol>";
}

echo "<h3>🚀 Quick Test:</h3>";
echo "<ol>";
echo "<li><a href='real_audio_test.php' style='color: #1976d2;'>Check system status</a></li>";
echo "<li><a href='index.php' style='color: #1976d2;'>Upload a real music file</a></li>";
echo "<li>Process and download results</li>";
echo "</ol>";

echo "</div>";

// Installation summary
echo "<h2>📋 Installation Summary</h2>";
echo "<table style='width: 100%; border-collapse: collapse; background: white; border-radius: 10px; overflow: hidden;'>";
echo "<tr style='background: #f5f5f5;'><th style='padding: 10px; text-align: left;'>Component</th><th style='padding: 10px; text-align: left;'>Status</th><th style='padding: 10px; text-align: left;'>Quality</th></tr>";

echo "<tr><td style='padding: 10px;'>Python 3.11</td><td style='padding: 10px;'>" . ($has_python ? "✅ Installed" : "❌ Missing") . "</td><td style='padding: 10px;'>Foundation</td></tr>";

echo "<tr><td style='padding: 10px;'>TensorFlow</td><td style='padding: 10px;'>" . ($has_tensorflow ? "✅ Installed" : "❌ Missing") . "</td><td style='padding: 10px;'>AI Ready</td></tr>";

echo "<tr><td style='padding: 10px;'>Spleeter</td><td style='padding: 10px;'>⚠️ Incompatible</td><td style='padding: 10px;'>Best Quality</td></tr>";

echo "<tr><td style='padding: 10px;'>FFmpeg</td><td style='padding: 10px;'>" . ($has_ffmpeg ? "✅ Installed" : "⚠️ PATH Issue") . "</td><td style='padding: 10px;'>Good Quality</td></tr>";

echo "<tr><td style='padding: 10px;'>Application</td><td style='padding: 10px;'>✅ Working</td><td style='padding: 10px;'>Demo/Basic</td></tr>";

echo "</table>";

echo "<h2>🎵 Next Steps</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 10px;'>";
echo "<h3>To Enable Real Audio Separation:</h3>";
echo "<ol>";
echo "<li><strong>Restart your terminal</strong> to activate FFmpeg PATH</li>";
echo "<li><strong>Test FFmpeg:</strong> <code>ffmpeg -version</code></li>";
echo "<li><strong>Upload real music files</strong> (not test files)</li>";
echo "<li><strong>Enjoy basic audio separation!</strong></li>";
echo "</ol>";

echo "<p><strong>Expected Result:</strong> Instead of 'tut tut tut', you'll get actual separated audio tracks using frequency filtering!</p>";
echo "</div>";

echo "</div>";

echo "<style>";
echo "code { background: #f1f1f1; padding: 2px 5px; border-radius: 3px; font-family: monospace; }";
echo "table { box-shadow: 0 2px 5px rgba(0,0,0,0.1); }";
echo "th { font-weight: bold; }";
echo "</style>";
?>
