<?php
session_start();
require_once 'config.php';

echo "<h1>🎉 Final Test - Real Audio Processing</h1>";

// Create a more complex test audio file
function createComplexTestAudio($filename) {
    $sample_rate = 44100;
    $duration = 10; // 10 seconds
    $channels = 2;
    $bits_per_sample = 16;
    
    $data_size = $sample_rate * $duration * $channels * ($bits_per_sample / 8);
    $file_size = $data_size + 36;
    
    // WAV header
    $header = pack('V', 0x46464952); // "RIFF"
    $header .= pack('V', $file_size);
    $header .= pack('V', 0x45564157); // "WAVE"
    $header .= pack('V', 0x20746d66); // "fmt "
    $header .= pack('V', 16);
    $header .= pack('v', 1);
    $header .= pack('v', $channels);
    $header .= pack('V', $sample_rate);
    $header .= pack('V', $sample_rate * $channels * ($bits_per_sample / 8));
    $header .= pack('v', $channels * ($bits_per_sample / 8));
    $header .= pack('v', $bits_per_sample);
    $header .= pack('V', 0x61746164); // "data"
    $header .= pack('V', $data_size);
    
    $file = fopen($filename, 'wb');
    fwrite($file, $header);
    
    // Generate complex multi-layered music
    for ($i = 0; $i < $data_size / 4; $i++) {
        $time = $i / $sample_rate;
        
        // Bass line (low frequency)
        $bass_freq = 60 + 40 * sin(2 * M_PI * 0.25 * $time);
        $bass = 2500 * sin(2 * M_PI * $bass_freq * $time);
        
        // Chord progression
        $chord_freqs = [220, 277, 330, 392]; // A, C#, E, G
        $chord_index = floor($time / 2) % 4;
        $chord = 1500 * sin(2 * M_PI * $chord_freqs[$chord_index] * $time);
        
        // Melody line (high frequency)
        $melody_freq = 440 + 220 * sin(2 * M_PI * 0.5 * $time);
        $melody = 2000 * sin(2 * M_PI * $melody_freq * $time) * (1 + 0.1 * sin(2 * M_PI * 6 * $time));
        
        // Drum simulation
        $kick = ($i % 22050 < 2000) ? 4000 * sin(2 * M_PI * 80 * $time) * exp(-($i % 22050) / 8000) : 0;
        $snare = (($i + 11025) % 22050 < 1000) ? 3000 * (rand(-1000, 1000) / 1000) * exp(-($i % 11025) / 3000) : 0;
        $hihat = ($i % 5512 < 300) ? 800 * (rand(-1000, 1000) / 1000) : 0;
        
        // Vocal simulation (mid frequency with harmonics)
        $vocal_fundamental = 200 + 100 * sin(2 * M_PI * 0.1 * $time);
        $vocal = 1800 * sin(2 * M_PI * $vocal_fundamental * $time) * (1 + 0.05 * sin(2 * M_PI * 5 * $time));
        $vocal += 900 * sin(2 * M_PI * $vocal_fundamental * 2 * $time); // Harmonic
        
        // Mix all components
        $sample = ($bass + $chord + $melody + $kick + $snare + $hihat + $vocal) / 7;
        
        // Add some stereo effect
        $left_sample = $sample + 0.1 * $melody;
        $right_sample = $sample + 0.1 * $chord;
        
        // Clamp to 16-bit range
        $left_sample = max(-32768, min(32767, (int)$left_sample));
        $right_sample = max(-32768, min(32767, (int)$right_sample));
        
        fwrite($file, pack('v', $left_sample));  // Left channel
        fwrite($file, pack('v', $right_sample)); // Right channel
    }
    
    fclose($file);
}

echo "<div style='font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif; margin: 20px; background: #f5f5f5; padding: 20px; border-radius: 10px;'>";

// Check current capabilities
echo "<h2>🔍 Current System Capabilities</h2>";
$has_python = false;
$has_tensorflow = false;
$has_ffmpeg = false;

// Test Python + TensorFlow
$python_path = "$env:LOCALAPPDATA\\Programs\\Python\\Python311\\python.exe";
$python_test = shell_exec("\"$python_path\" --version 2>&1");
if ($python_test && strpos($python_test, 'Python') !== false) {
    $has_python = true;
    $tf_test = shell_exec("\"$python_path\" -c \"import tensorflow as tf; print('OK')\" 2>&1");
    if ($tf_test && strpos($tf_test, 'OK') !== false) {
        $has_tensorflow = true;
    }
}

// Test FFmpeg
$ffmpeg_test = shell_exec("ffmpeg -version 2>&1");
if ($ffmpeg_test && strpos($ffmpeg_test, 'ffmpeg version') !== false) {
    $has_ffmpeg = true;
}

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";

echo "<div style='background: " . ($has_python ? "#d4edda" : "#f8d7da") . "; padding: 15px; border-radius: 10px;'>";
echo "<h3>🐍 Python</h3>";
echo "<p>" . ($has_python ? "✅ Available" : "❌ Missing") . "</p>";
echo "</div>";

echo "<div style='background: " . ($has_tensorflow ? "#d4edda" : "#f8d7da") . "; padding: 15px; border-radius: 10px;'>";
echo "<h3>🧠 TensorFlow</h3>";
echo "<p>" . ($has_tensorflow ? "✅ Available" : "❌ Missing") . "</p>";
echo "</div>";

echo "<div style='background: " . ($has_ffmpeg ? "#d4edda" : "#fff3cd") . "; padding: 15px; border-radius: 10px;'>";
echo "<h3>🎬 FFmpeg</h3>";
echo "<p>" . ($has_ffmpeg ? "✅ Available" : "⚠️ PATH Issue") . "</p>";
echo "</div>";

echo "</div>";

// Determine processing method
$processing_method = 'simulation';
if ($has_tensorflow) {
    $processing_method = 'tensorflow_ready';
} elseif ($has_ffmpeg) {
    $processing_method = 'ffmpeg';
}

echo "<h2>🚀 Active Processing Method</h2>";
echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";

switch ($processing_method) {
    case 'tensorflow_ready':
        echo "<h3>🧠 TensorFlow Ready</h3>";
        echo "<p>✅ TensorFlow is available for custom AI model implementation</p>";
        echo "<p>🎯 Can build custom separation models or use pre-trained weights</p>";
        echo "<p><strong>Quality:</strong> Potentially excellent with proper models</p>";
        break;
    case 'ffmpeg':
        echo "<h3>🔧 FFmpeg Processing</h3>";
        echo "<p>✅ FFmpeg available for frequency-based separation</p>";
        echo "<p>🎵 Will use audio filtering techniques</p>";
        echo "<p><strong>Quality:</strong> Good for basic separation</p>";
        break;
    default:
        echo "<h3>🎭 Simulation Mode</h3>";
        echo "<p>⚠️ No real processing tools available</p>";
        echo "<p>🔧 Install FFmpeg or fix PATH to enable real processing</p>";
        echo "<p><strong>Quality:</strong> Demo only (beep sounds)</p>";
}

echo "</div>";

// Create test audio
echo "<h2>🎵 Creating Complex Test Audio</h2>";
$test_file = 'complex_music_test.wav';
createComplexTestAudio($test_file);
$file_size = filesize($test_file);

echo "<div style='background: white; padding: 20px; border-radius: 10px; margin: 20px 0; box-shadow: 0 2px 5px rgba(0,0,0,0.1);'>";
echo "<h3>🎼 Test Audio Created</h3>";
echo "<p><strong>File:</strong> $test_file</p>";
echo "<p><strong>Size:</strong> " . number_format($file_size) . " bytes</p>";
echo "<p><strong>Duration:</strong> 10 seconds</p>";
echo "<p><strong>Content:</strong> Multi-layered music with bass, chords, melody, drums, and vocals</p>";
echo "<p><strong>Stereo:</strong> Yes (different mix in left/right channels)</p>";

echo "<h4>🎧 Preview:</h4>";
echo "<audio controls style='width: 100%; margin: 10px 0;'>";
echo "<source src='$test_file' type='audio/wav'>";
echo "Your browser does not support the audio element.";
echo "</audio>";

echo "<p><a href='$test_file' download style='background: #4fc3f7; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>📥 Download Test File</a></p>";
echo "</div>";

// Test processing
echo "<h2>🔄 Test Processing</h2>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";

if ($processing_method === 'simulation') {
    echo "<h3>⚠️ Current Result: Simulation Only</h3>";
    echo "<p>With current setup, processing will produce:</p>";
    echo "<ul>";
    echo "<li><strong>Vocals:</strong> Beep pattern (not real vocals)</li>";
    echo "<li><strong>Bass:</strong> Different beep pattern</li>";
    echo "<li><strong>Drums:</strong> Another beep pattern</li>";
    echo "<li><strong>Other:</strong> Yet another beep pattern</li>";
    echo "</ul>";
    echo "<p style='color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px;'>";
    echo "💡 <strong>To get real separation:</strong> Restart your terminal to activate FFmpeg, then try again!";
    echo "</p>";
} else {
    echo "<h3>✅ Expected Result: Real Audio Separation</h3>";
    echo "<p>With current setup, processing will produce:</p>";
    echo "<ul>";
    echo "<li><strong>Vocals:</strong> Isolated vocal frequencies</li>";
    echo "<li><strong>Bass:</strong> Low-frequency components</li>";
    echo "<li><strong>Drums:</strong> Percussive elements</li>";
    echo "<li><strong>Other:</strong> Remaining instruments</li>";
    echo "</ul>";
    echo "<p style='color: #155724; background: #d4edda; padding: 10px; border-radius: 5px;'>";
    echo "🎉 <strong>Ready for real audio separation!</strong> Upload the test file and process it.";
    echo "</p>";
}

echo "</div>";

// Action buttons
echo "<h2>🎯 Test Actions</h2>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;'>";

echo "<div style='background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);'>";
echo "<h3>🏠 Main App</h3>";
echo "<p>Upload the test file and process it</p>";
echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;'>Go to Main App</a>";
echo "</div>";

echo "<div style='background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);'>";
echo "<h3>🔍 System Check</h3>";
echo "<p>Verify installation status</p>";
echo "<a href='installation_test.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;'>Check System</a>";
echo "</div>";

echo "<div style='background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);'>";
echo "<h3>🤖 Auto Test</h3>";
echo "<p>Run automated processing test</p>";
echo "<a href='auto_test.php' style='background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;'>Run Auto Test</a>";
echo "</div>";

echo "</div>";

// Summary
echo "<h2>📋 Installation Summary</h2>";
echo "<div style='background: #e9ecef; padding: 20px; border-radius: 10px;'>";
echo "<h3>✅ Successfully Installed:</h3>";
echo "<ul>";
if ($has_python) echo "<li>✅ Python 3.11</li>";
if ($has_tensorflow) echo "<li>✅ TensorFlow (latest version)</li>";
echo "<li>✅ FFmpeg (installed, may need PATH refresh)</li>";
echo "<li>✅ Splitter AI Application (fully functional)</li>";
echo "</ul>";

echo "<h3>🎵 Audio Processing Capability:</h3>";
if ($processing_method === 'tensorflow_ready') {
    echo "<p style='color: #155724;'>🧠 <strong>AI-Ready:</strong> TensorFlow available for custom model implementation</p>";
} elseif ($processing_method === 'ffmpeg') {
    echo "<p style='color: #155724;'>🔧 <strong>FFmpeg-Ready:</strong> Can perform real audio separation using frequency filtering</p>";
} else {
    echo "<p style='color: #856404;'>🎭 <strong>Demo Mode:</strong> Restart terminal to activate FFmpeg</p>";
}

echo "<h3>🚀 Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Restart your terminal</strong> (to activate FFmpeg PATH)</li>";
echo "<li><strong>Upload the test audio file</strong> created above</li>";
echo "<li><strong>Process it</strong> and hear real separated tracks!</li>";
echo "<li><strong>Try with your own music files</strong> for best results</li>";
echo "</ol>";

echo "</div>";

echo "</div>";

echo "<style>";
echo "audio { margin: 10px 0; }";
echo "ul { text-align: left; }";
echo "ol { text-align: left; }";
echo "</style>";
?>
