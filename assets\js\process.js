document.addEventListener('DOMContentLoaded', function() {
    const progressBar = document.getElementById('progressBar');
    const progressPercentage = document.getElementById('progressPercentage');
    const progressMessage = document.getElementById('progressMessage');
    const statusSection = document.getElementById('statusSection');
    const resultsSection = document.getElementById('resultsSection');
    
    let currentAudio = null;
    let progressInterval = null;

    // Start progress monitoring if processing
    if (currentStatus === 'processing') {
        startProgressMonitoring();
    }

    function startProgressMonitoring() {
        progressInterval = setInterval(updateProgress, 1000);
    }

    function updateProgress() {
        fetch(`process.php?id=${processingId}&action=progress`)
            .then(response => response.json())
            .then(data => {
                // Update progress bar
                progressBar.style.width = data.progress + '%';
                progressPercentage.textContent = data.progress + '%';
                progressMessage.textContent = data.message;

                // Update status
                updateStatus(data.status);

                // If completed, stop monitoring and show results
                if (data.status === 'completed') {
                    clearInterval(progressInterval);
                    showResults();
                }
            })
            .catch(error => {
                console.error('Error updating progress:', error);
                clearInterval(progressInterval);
            });
    }

    function updateStatus(status) {
        let statusHTML = '';
        
        switch (status) {
            case 'processing':
                statusHTML = `
                    <div class="status-item processing">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>Processing in progress...</span>
                    </div>
                `;
                break;
            case 'completed':
                statusHTML = `
                    <div class="status-item completed">
                        <i class="fas fa-check-circle"></i>
                        <span>Processing completed successfully!</span>
                    </div>
                `;
                break;
            default:
                statusHTML = `
                    <div class="status-item waiting">
                        <i class="fas fa-clock"></i>
                        <span>Waiting to start...</span>
                    </div>
                `;
        }
        
        statusSection.innerHTML = statusHTML;
    }

    function showResults() {
        // If results section doesn't exist, create it
        if (!resultsSection) {
            createResultsSection();
        } else {
            resultsSection.style.display = 'block';
            resultsSection.style.animation = 'fadeInUp 0.5s ease';
        }
        
        // Add event listeners to track controls
        setupTrackControls();
    }

    function createResultsSection() {
        const resultsHTML = `
            <div class="results-section" id="resultsSection">
                <h2>Separated Tracks</h2>
                <div class="tracks-grid">
                    <div class="track-card">
                        <div class="track-icon vocals">
                            <i class="fas fa-microphone"></i>
                        </div>
                        <h3>Vocals</h3>
                        <div class="track-controls">
                            <button class="play-btn" data-track="vocals">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="download-btn" data-track="vocals">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="track-card">
                        <div class="track-icon bass">
                            <i class="fas fa-guitar"></i>
                        </div>
                        <h3>Bass</h3>
                        <div class="track-controls">
                            <button class="play-btn" data-track="bass">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="download-btn" data-track="bass">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="track-card">
                        <div class="track-icon drums">
                            <i class="fas fa-drum"></i>
                        </div>
                        <h3>Drums</h3>
                        <div class="track-controls">
                            <button class="play-btn" data-track="drums">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="download-btn" data-track="drums">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="track-card">
                        <div class="track-icon other">
                            <i class="fas fa-music"></i>
                        </div>
                        <h3>Other</h3>
                        <div class="track-controls">
                            <button class="play-btn" data-track="other">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="download-btn" data-track="other">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="download-all-section">
                    <button class="download-all-btn">
                        <i class="fas fa-download"></i>
                        Download All Tracks
                    </button>
                </div>
            </div>
        `;
        
        document.querySelector('.processing-section').insertAdjacentHTML('beforeend', resultsHTML);
        setupTrackControls();
    }

    function setupTrackControls() {
        // Play button functionality
        const playButtons = document.querySelectorAll('.play-btn');
        playButtons.forEach(button => {
            button.addEventListener('click', function() {
                const track = this.dataset.track;
                togglePlayback(this, track);
            });
        });

        // Download button functionality
        const downloadButtons = document.querySelectorAll('.download-btn');
        downloadButtons.forEach(button => {
            button.addEventListener('click', function() {
                const track = this.dataset.track;
                downloadTrack(track);
            });
        });

        // Download all button
        const downloadAllBtn = document.querySelector('.download-all-btn');
        if (downloadAllBtn) {
            downloadAllBtn.addEventListener('click', downloadAllTracks);
        }
    }

    function togglePlayback(button, track) {
        const icon = button.querySelector('i');
        
        // Stop current audio if playing
        if (currentAudio && !currentAudio.paused) {
            currentAudio.pause();
            currentAudio.currentTime = 0;
            // Reset all play buttons
            document.querySelectorAll('.play-btn i').forEach(i => {
                i.classList.remove('fa-pause');
                i.classList.add('fa-play');
            });
        }

        // If this was the playing track, just stop
        if (icon.classList.contains('fa-pause')) {
            icon.classList.remove('fa-pause');
            icon.classList.add('fa-play');
            return;
        }

        // Create audio element for the track (in real app, this would be actual separated audio)
        // For demo purposes, we'll simulate playback
        simulatePlayback(button, track);
    }

    function simulatePlayback(button, track) {
        const icon = button.querySelector('i');
        
        // Change icon to pause
        icon.classList.remove('fa-play');
        icon.classList.add('fa-pause');
        
        // Simulate playback duration (30 seconds)
        setTimeout(() => {
            icon.classList.remove('fa-pause');
            icon.classList.add('fa-play');
        }, 30000);
        
        // In real implementation, you would:
        // currentAudio = new Audio(`output/${processingId}/${track}.wav`);
        // currentAudio.play();
        // currentAudio.addEventListener('ended', () => {
        //     icon.classList.remove('fa-pause');
        //     icon.classList.add('fa-play');
        // });
    }

    function downloadTrack(track) {
        // Create download link
        const link = document.createElement('a');
        link.href = `download.php?id=${processingId}&track=${track}`;
        link.download = `${track}.wav`;
        link.style.display = 'none';
        document.body.appendChild(link);

        // Trigger download
        link.click();

        // Clean up
        setTimeout(() => {
            document.body.removeChild(link);
        }, 100);

        // Show download feedback
        showDownloadFeedback(`${track} track download started`);
    }

    function downloadAllTracks() {
        // Create download link for ZIP
        const link = document.createElement('a');
        link.href = `download.php?id=${processingId}&all=true`;
        link.download = `separated_tracks.zip`;
        link.style.display = 'none';
        document.body.appendChild(link);

        // Trigger download
        link.click();

        // Clean up
        setTimeout(() => {
            document.body.removeChild(link);
        }, 100);

        showDownloadFeedback('All tracks download started');
    }

    function showDownloadFeedback(message) {
        // Create temporary feedback message
        const feedback = document.createElement('div');
        feedback.className = 'download-feedback';
        feedback.innerHTML = `
            <i class="fas fa-check-circle"></i>
            ${message}
        `;
        feedback.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 1000;
            animation: slideInRight 0.3s ease;
        `;
        
        document.body.appendChild(feedback);
        
        // Remove after 3 seconds
        setTimeout(() => {
            feedback.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(feedback);
            }, 300);
        }, 3000);
    }

    // Add CSS animations for feedback
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);

    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        if (progressInterval) {
            clearInterval(progressInterval);
        }
        if (currentAudio) {
            currentAudio.pause();
        }
    });
});
