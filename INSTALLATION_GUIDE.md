# 🎵 Splitter AI - Installation Guide untuk Audio Separation Sesungguhnya

Saat ini aplikasi menghasilkan suara "tut tut tut" karena menggunakan simulasi. Untuk mendapatkan pemisahan audio yang sesungguhnya, ikuti panduan instalasi di bawah ini.

## 🎯 Pilihan Metode Pemisahan Audio

### 1. **Spleeter (RECOMMENDED) - Kualitas Terbaik**
- ✅ Menggunakan AI/Machine Learning
- ✅ Hasil pemisahan sangat baik
- ✅ Dilatih dengan ribuan lagu
- ❌ Membutuhkan Python dan dependencies

### 2. **FFmpeg - Kualitas Menengah**
- ✅ Tidak perlu Python
- ✅ Lebih ringan
- ⚠️ Hasil basic (frequency filtering)
- ⚠️ Tidak sebaik AI-based

### 3. **Simulasi - Demo Saja**
- ❌ Hanya menghasilkan beep sounds
- ✅ Tidak perlu instalasi tambahan

## 🚀 Instalasi Spleeter (Recommended)

### Step 1: Install Python
```bash
# Windows (menggunakan Chocolatey)
choco install python

# Atau download dari python.org
# Pastikan Python 3.7+ terinstall
python --version
```

### Step 2: Install Spleeter
```bash
pip install spleeter
pip install tensorflow
```

### Step 3: Test Instalasi
```bash
spleeter --version
```

### Step 4: Download Models (Otomatis saat pertama kali digunakan)
```bash
# Model akan didownload otomatis saat pertama kali memproses
# Atau download manual:
spleeter separate -p spleeter:4stems-16kHz --help
```

## 🔧 Instalasi FFmpeg (Alternative)

### Windows
```bash
# Menggunakan Chocolatey
choco install ffmpeg

# Atau download dari https://ffmpeg.org/download.html
```

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install ffmpeg
```

### macOS
```bash
# Menggunakan Homebrew
brew install ffmpeg
```

### Test Instalasi
```bash
ffmpeg -version
```

## ⚙️ Konfigurasi Aplikasi

Edit file `config.php`:

```php
// Untuk menggunakan Spleeter atau FFmpeg
define('USE_LOCAL_PROCESSING', true);

// Path ke executable (sesuaikan jika perlu)
define('SPLEETER_PATH', 'spleeter');
define('FFMPEG_PATH', 'ffmpeg');
define('FFPROBE_PATH', 'ffprobe');

// Model Spleeter yang digunakan
define('SPLEETER_MODEL', '4stems-16kHz'); // 2stems, 4stems, atau 5stems
```

## 🎵 Model Spleeter yang Tersedia

### 2stems-16kHz
- **Vocals**: Suara vokal
- **Accompaniment**: Semua instrumen musik

### 4stems-16kHz (Default)
- **Vocals**: Suara vokal
- **Bass**: Bass guitar dan instrumen low-frequency
- **Drums**: Drum kit dan perkusi
- **Other**: Instrumen lainnya (guitar, piano, dll)

### 5stems-16kHz
- **Vocals**: Suara vokal
- **Bass**: Bass guitar
- **Drums**: Drum kit
- **Piano**: Piano dan keyboard
- **Other**: Instrumen lainnya

## 🧪 Testing

1. **Test instalasi**:
   ```
   http://localhost:8000/real_audio_test.php
   ```

2. **Upload audio asli**:
   - Gunakan file MP3/WAV dari lagu sesungguhnya
   - Bukan file test yang dibuat aplikasi

3. **Hasil yang diharapkan**:
   - **Dengan Spleeter**: Audio terpisah dengan kualitas tinggi
   - **Dengan FFmpeg**: Audio terpisah dengan kualitas basic
   - **Tanpa keduanya**: Masih "tut tut tut" (simulasi)

## 🔍 Troubleshooting

### Spleeter Issues
```bash
# Jika error tensorflow
pip install tensorflow==2.8.0

# Jika error model download
pip install --upgrade spleeter

# Jika error CUDA (optional, untuk GPU acceleration)
pip install tensorflow-gpu
```

### FFmpeg Issues
```bash
# Test FFmpeg
ffmpeg -version
ffprobe -version

# Jika command not found, tambahkan ke PATH
# Windows: Add FFmpeg bin folder to System PATH
# Linux/Mac: sudo ln -s /path/to/ffmpeg /usr/local/bin/
```

### Permission Issues
```bash
# Linux/Mac
chmod +x /usr/local/bin/spleeter
chmod +x /usr/local/bin/ffmpeg

# Windows: Run as Administrator
```

## 📁 Struktur File Hasil

Setelah instalasi yang benar:

```
output/
├── processing_id_123/
│   ├── vocals.wav      # Suara vokal yang terpisah
│   ├── bass.wav        # Bass line yang terpisah  
│   ├── drums.wav       # Drum track yang terpisah
│   └── other.wav       # Instrumen lain yang terpisah
```

## 🎯 Quick Start

1. **Install Spleeter**:
   ```bash
   pip install spleeter tensorflow
   ```

2. **Test**:
   ```bash
   spleeter --version
   ```

3. **Upload lagu asli** di aplikasi (bukan file test)

4. **Enjoy** hasil pemisahan audio yang sesungguhnya!

## 💡 Tips

- **Gunakan file audio berkualitas tinggi** (WAV/FLAC) untuk hasil terbaik
- **Spleeter membutuhkan internet** untuk download model pertama kali
- **Processing time** sekitar 1-3 menit tergantung panjang lagu
- **GPU acceleration** akan mempercepat processing jika tersedia

## 🆘 Butuh Bantuan?

Jika masih menghasilkan "tut tut tut":
1. Cek `real_audio_test.php` untuk status instalasi
2. Pastikan menggunakan file audio asli, bukan file test
3. Periksa log error di `logs/error.log`
4. Test command line: `spleeter --version` atau `ffmpeg -version`
