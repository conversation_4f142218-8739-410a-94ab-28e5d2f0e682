<?php
session_start();
require_once 'config.php';

// Handle file upload
$upload_message = '';
$processing_id = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['audio_file'])) {
    $upload_result = handleFileUpload($_FILES['audio_file']);
    if ($upload_result['success']) {
        $processing_id = $upload_result['processing_id'];
        header('Location: process.php?id=' . $processing_id);
        exit;
    } else {
        $upload_message = $upload_result['message'];
    }
}

function handleFileUpload($file) {
    // Check for upload errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'Upload error occurred.'];
    }
    
    // Check file size
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'File size exceeds maximum limit (50MB).'];
    }
    
    // Check file extension
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, ALLOWED_EXTENSIONS)) {
        return ['success' => false, 'message' => 'Invalid file format. Supported: ' . implode(', ', ALLOWED_EXTENSIONS)];
    }
    
    // Generate unique processing ID
    $processing_id = uniqid('audio_', true);
    $upload_path = UPLOAD_DIR . $processing_id . '.' . $file_extension;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        // Store processing info in session
        $_SESSION['processing'][$processing_id] = [
            'original_name' => $file['name'],
            'file_path' => $upload_path,
            'status' => 'uploaded',
            'created_at' => time()
        ];
        
        return ['success' => true, 'processing_id' => $processing_id];
    } else {
        return ['success' => false, 'message' => 'Failed to save uploaded file.'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Splitter AI - Split music into separated parts</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Navigation -->
        <nav class="navbar">
            <div class="nav-brand">
                <h1>Splitter AI</h1>
            </div>
            <div class="nav-menu">
                <a href="#" class="nav-link active">Splitter</a>
                <a href="#" class="nav-link">How it works</a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="hero-section">
                <h1 class="hero-title">Splitter AI</h1>
                <p class="hero-subtitle">Split music into separated parts with AI-Powered algorithms</p>
                
                <!-- Audio Visualization Preview -->
                <div class="audio-preview">
                    <div class="track-preview">
                        <div class="track-label">Music</div>
                        <div class="waveform music-wave"></div>
                        <div class="volume-control">
                            <i class="fas fa-volume-up"></i>
                        </div>
                    </div>
                    <div class="track-preview">
                        <div class="track-label">Vocal</div>
                        <div class="waveform vocal-wave"></div>
                        <div class="volume-control">
                            <i class="fas fa-volume-up"></i>
                        </div>
                    </div>
                    <div class="track-preview">
                        <div class="track-label">Bass</div>
                        <div class="waveform bass-wave"></div>
                        <div class="volume-control">
                            <i class="fas fa-volume-up"></i>
                        </div>
                    </div>
                    <div class="track-preview">
                        <div class="track-label">Drums</div>
                        <div class="waveform drums-wave"></div>
                        <div class="volume-control">
                            <i class="fas fa-volume-up"></i>
                        </div>
                    </div>
                </div>

                <!-- File Upload Section -->
                <div class="upload-section">
                    <?php if ($upload_message): ?>
                        <div class="error-message">
                            <i class="fas fa-exclamation-triangle"></i>
                            <?php echo htmlspecialchars($upload_message); ?>
                        </div>
                    <?php endif; ?>
                    
                    <form id="uploadForm" method="POST" enctype="multipart/form-data" class="upload-form">
                        <div class="file-input-wrapper">
                            <input type="file" id="audio_file" name="audio_file" accept=".mp3,.wav,.flac,.m4a,.ogg" required>
                            <label for="audio_file" class="file-input-label">
                                <i class="fas fa-folder-open"></i>
                                <span>Browse my files</span>
                            </label>
                        </div>
                        <div class="file-info" id="fileInfo" style="display: none;">
                            <div class="file-details">
                                <i class="fas fa-music"></i>
                                <span id="fileName"></span>
                                <span id="fileSize"></span>
                            </div>
                            <button type="submit" class="process-btn">
                                <i class="fas fa-magic"></i>
                                Process Audio
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Info Section -->
                <div class="info-section">
                    <h2>AI-Powered Music Separator</h2>
                    <p>This app allows to separate music into individual streams such as vocal, bass, percussion, and lets you rebalance their individual volumes. This is the simplest way to get multitracks from any song.</p>
                    <p>Once you choose a song, artificial intelligence will separate music into stems: vocals, bass, drums, others. Processing usually takes about 1 minute.</p>
                </div>
            </div>
        </main>
    </div>

    <script src="assets/js/main.js"></script>
</body>
</html>
