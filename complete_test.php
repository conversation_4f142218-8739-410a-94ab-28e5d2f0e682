<?php
session_start();
require_once 'config.php';

echo "<h1>Complete Flow Test</h1>";

// Step 1: Create test audio file
function createTestAudioFile($filename) {
    $sample_rate = 44100;
    $duration = 3; // 3 seconds
    $channels = 2;
    $bits_per_sample = 16;
    
    $data_size = $sample_rate * $duration * $channels * ($bits_per_sample / 8);
    $file_size = $data_size + 36;
    
    $header = pack('V', 0x46464952); // "RIFF"
    $header .= pack('V', $file_size);
    $header .= pack('V', 0x45564157); // "WAVE"
    $header .= pack('V', 0x20746d66); // "fmt "
    $header .= pack('V', 16);
    $header .= pack('v', 1);
    $header .= pack('v', $channels);
    $header .= pack('V', $sample_rate);
    $header .= pack('V', $sample_rate * $channels * ($bits_per_sample / 8));
    $header .= pack('v', $channels * ($bits_per_sample / 8));
    $header .= pack('v', $bits_per_sample);
    $header .= pack('V', 0x61746164); // "data"
    $header .= pack('V', $data_size);
    
    $file = fopen($filename, 'wb');
    fwrite($file, $header);
    
    for ($i = 0; $i < $data_size / 4; $i++) {
        $time = $i / $sample_rate;
        $sample = (int)(3000 * sin(2 * M_PI * 440 * $time));
        fwrite($file, pack('v', $sample));
        fwrite($file, pack('v', $sample));
    }
    
    fclose($file);
}

// Step 2: Simulate upload process
$processing_id = 'complete_test_' . uniqid();
$test_audio_file = UPLOAD_DIR . $processing_id . '.wav';
$output_path = OUTPUT_DIR . $processing_id . '/';

// Create directories
if (!file_exists(UPLOAD_DIR)) mkdir(UPLOAD_DIR, 0755, true);
if (!file_exists($output_path)) mkdir($output_path, 0755, true);

// Create test audio file
createTestAudioFile($test_audio_file);
echo "<p>✓ Step 1: Created test audio file ($test_audio_file)</p>";

// Step 3: Simulate processing
$_SESSION['processing'][$processing_id] = [
    'original_name' => 'complete_test.wav',
    'file_path' => $test_audio_file,
    'status' => 'processing',
    'progress' => 0,
    'message' => 'Starting...',
    'created_at' => time()
];

echo "<p>✓ Step 2: Created processing session</p>";

// Step 4: Create output files
function createDummyWavFile($filepath, $track_type) {
    $sample_rate = 44100;
    $duration = 3;
    $channels = 2;
    $bits_per_sample = 16;
    
    $data_size = $sample_rate * $duration * $channels * ($bits_per_sample / 8);
    $file_size = $data_size + 36;
    
    $header = pack('V', 0x46464952); // "RIFF"
    $header .= pack('V', $file_size);
    $header .= pack('V', 0x45564157); // "WAVE"
    $header .= pack('V', 0x20746d66); // "fmt "
    $header .= pack('V', 16);
    $header .= pack('v', 1);
    $header .= pack('v', $channels);
    $header .= pack('V', $sample_rate);
    $header .= pack('V', $sample_rate * $channels * ($bits_per_sample / 8));
    $header .= pack('v', $channels * ($bits_per_sample / 8));
    $header .= pack('v', $bits_per_sample);
    $header .= pack('V', 0x61746164); // "data"
    $header .= pack('V', $data_size);
    
    $file = fopen($filepath, 'wb');
    fwrite($file, $header);
    
    // Different frequencies for different tracks
    $frequencies = [
        'vocals' => 800,
        'bass' => 200,
        'drums' => 1000,
        'other' => 600
    ];
    
    $freq = $frequencies[$track_type] ?? 440;
    
    for ($i = 0; $i < $data_size / 4; $i++) {
        $time = $i / $sample_rate;
        $sample = (int)(4000 * sin(2 * M_PI * $freq * $time));
        fwrite($file, pack('v', $sample));
        fwrite($file, pack('v', $sample));
    }
    
    fclose($file);
}

$tracks = ['vocals', 'bass', 'drums', 'other'];
foreach ($tracks as $track) {
    $track_file = $output_path . $track . '.wav';
    createDummyWavFile($track_file, $track);
    echo "<p>✓ Created $track track (" . filesize($track_file) . " bytes)</p>";
}

// Step 5: Update session to completed
$_SESSION['processing'][$processing_id]['status'] = 'completed';
$_SESSION['processing'][$processing_id]['progress'] = 100;
$_SESSION['processing'][$processing_id]['message'] = 'Processing completed successfully!';
$_SESSION['processing'][$processing_id]['output_files'] = [
    'vocals' => $output_path . 'vocals.wav',
    'bass' => $output_path . 'bass.wav',
    'drums' => $output_path . 'drums.wav',
    'other' => $output_path . 'other.wav'
];

echo "<p>✓ Step 3: Processing completed</p>";

// Step 6: Test download links
echo "<h2>Test Downloads</h2>";
foreach ($tracks as $track) {
    $download_url = "download.php?id=$processing_id&track=$track";
    echo "<p><a href='$download_url' target='_blank'>Download $track</a></p>";
}

echo "<p><a href='download.php?id=$processing_id&all=true' target='_blank'>Download All (ZIP)</a></p>";

// Step 7: Test process page
echo "<h2>Test Process Page</h2>";
echo "<p><a href='process.php?id=$processing_id' target='_blank'>View Process Page</a></p>";

// Step 8: Session info
echo "<h2>Session Information</h2>";
echo "<pre>";
print_r($_SESSION['processing'][$processing_id]);
echo "</pre>";

// Save session
session_write_close();

echo "<h2>Test Complete!</h2>";
echo "<p>Processing ID: <strong>$processing_id</strong></p>";
?>
