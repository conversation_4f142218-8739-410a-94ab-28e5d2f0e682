/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    min-height: 100vh;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-brand h1 {
    font-size: 24px;
    font-weight: 700;
    color: #4fc3f7;
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #4fc3f7;
}

/* Main Content */
.main-content {
    padding: 60px 0;
}

.hero-section {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 18px;
    color: #b0bec5;
    margin-bottom: 50px;
}

/* Audio Preview */
.audio-preview {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 50px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.track-preview {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.track-preview:hover {
    transform: translateX(5px);
}

.track-preview:last-child {
    margin-bottom: 0;
}

.track-label {
    width: 80px;
    text-align: left;
    font-weight: 600;
    color: #ffffff;
}

.waveform {
    flex: 1;
    height: 40px;
    margin: 0 20px;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.music-wave {
    background: linear-gradient(90deg, #4caf50, #8bc34a);
}

.vocal-wave {
    background: linear-gradient(90deg, #9c27b0, #e91e63);
}

.bass-wave {
    background: linear-gradient(90deg, #ff9800, #ffc107);
}

.drums-wave {
    background: linear-gradient(90deg, #f44336, #ff5722);
}

.waveform::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 2px,
        rgba(255, 255, 255, 0.1) 2px,
        rgba(255, 255, 255, 0.1) 4px
    );
    animation: waveAnimation 2s linear infinite;
}

@keyframes waveAnimation {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.volume-control {
    width: 40px;
    text-align: center;
    color: #4fc3f7;
    cursor: pointer;
    transition: color 0.3s ease;
}

.volume-control:hover {
    color: #29b6f6;
}

/* Upload Section */
.upload-section {
    margin-bottom: 50px;
}

.error-message {
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid #f44336;
    color: #f44336;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.upload-form {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 40px;
    border: 2px dashed rgba(79, 195, 247, 0.3);
    transition: all 0.3s ease;
}

.upload-form:hover {
    border-color: rgba(79, 195, 247, 0.6);
    background: rgba(255, 255, 255, 0.08);
}

.file-input-wrapper {
    position: relative;
    margin-bottom: 20px;
}

#audio_file {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-input-label {
    display: inline-flex;
    align-items: center;
    gap: 15px;
    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
    color: white;
    padding: 15px 30px;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
}

.file-input-label:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 195, 247, 0.4);
}

.file-info {
    text-align: center;
}

.file-details {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.process-btn {
    background: linear-gradient(45deg, #4caf50, #8bc34a);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.process-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

/* Info Section */
.info-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 40px;
    text-align: left;
}

.info-section h2 {
    color: #4fc3f7;
    margin-bottom: 20px;
    font-size: 24px;
}

.info-section p {
    color: #b0bec5;
    margin-bottom: 15px;
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 36px;
    }
    
    .hero-subtitle {
        font-size: 16px;
    }
    
    .track-preview {
        flex-direction: column;
        text-align: center;
    }
    
    .track-label {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .waveform {
        margin: 10px 0;
    }
    
    .upload-form {
        padding: 20px;
    }
    
    .nav-menu {
        display: none;
    }
}
