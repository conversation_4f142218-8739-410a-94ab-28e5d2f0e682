<?php
// Configuration file for Splitter AI

// Application settings
define('APP_NAME', 'Splitter AI');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/spliter'); // Change this to your domain

// File upload settings
define('UPLOAD_DIR', 'uploads/');
define('OUTPUT_DIR', 'output/');
define('TEMP_DIR', 'temp/');
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
define('ALLOWED_EXTENSIONS', ['mp3', 'wav', 'flac', 'm4a', 'ogg', 'aac']);
define('ALLOWED_MIME_TYPES', [
    'audio/mpeg',
    'audio/wav', 
    'audio/x-wav',
    'audio/flac',
    'audio/mp4',
    'audio/ogg',
    'audio/aac'
]);

// Processing settings
define('PROCESSING_TIMEOUT', 300); // 5 minutes
define('MAX_CONCURRENT_PROCESSES', 3);
define('CLEANUP_AGE', 24 * 60 * 60); // 24 hours

// AI Processing settings (for future integration)
define('AI_SERVICE_URL', ''); // URL for AI processing service
define('AI_API_KEY', ''); // API key for AI service
define('USE_LOCAL_PROCESSING', true); // Use local FFmpeg instead of API

// FFmpeg settings (if using local processing)
define('FFMPEG_PATH', 'ffmpeg'); // Path to FFmpeg binary
define('FFPROBE_PATH', 'ffprobe'); // Path to FFprobe binary

// Spleeter settings (if using Spleeter)
define('SPLEETER_PATH', 'spleeter'); // Path to Spleeter
define('SPLEETER_MODEL', '2stems-16kHz'); // Default model: 2stems, 4stems, 5stems

// Database settings (for future use)
define('DB_HOST', 'localhost');
define('DB_NAME', 'splitter_ai');
define('DB_USER', 'root');
define('DB_PASS', '');

// Security settings
define('SESSION_LIFETIME', 3600); // 1 hour
define('CSRF_TOKEN_LENGTH', 32);
define('MAX_UPLOAD_ATTEMPTS', 5);
define('RATE_LIMIT_WINDOW', 3600); // 1 hour
define('RATE_LIMIT_MAX_REQUESTS', 10);

// Email settings (for notifications)
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USER', '');
define('SMTP_PASS', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Splitter AI');

// Error reporting
define('DEBUG_MODE', true); // Set to false in production
define('LOG_ERRORS', true);
define('ERROR_LOG_FILE', 'logs/error.log');

// Create necessary directories
$directories = [
    UPLOAD_DIR,
    OUTPUT_DIR,
    TEMP_DIR,
    'logs/'
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Set error reporting based on debug mode
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Set session configuration
ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
ini_set('session.cookie_lifetime', SESSION_LIFETIME);

// Function to get supported audio formats for display
function getSupportedFormats() {
    return implode(', ', ALLOWED_EXTENSIONS);
}

// Function to format file size
function formatFileSize($bytes) {
    if ($bytes === 0) return '0 Bytes';
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB'];
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

// Function to generate CSRF token
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(CSRF_TOKEN_LENGTH));
    }
    return $_SESSION['csrf_token'];
}

// Function to verify CSRF token
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Function to log errors
function logError($message, $file = __FILE__, $line = __LINE__) {
    if (LOG_ERRORS) {
        $timestamp = date('Y-m-d H:i:s');
        $log_message = "[{$timestamp}] Error in {$file}:{$line} - {$message}" . PHP_EOL;
        file_put_contents(ERROR_LOG_FILE, $log_message, FILE_APPEND | LOCK_EX);
    }
}

// Function to check if FFmpeg is available
function isFFmpegAvailable() {
    $output = [];
    $return_var = 0;
    exec(FFMPEG_PATH . ' -version 2>&1', $output, $return_var);
    return $return_var === 0;
}

// Function to check if Spleeter is available
function isSpleeterAvailable() {
    $output = [];
    $return_var = 0;
    exec(SPLEETER_PATH . ' --version 2>&1', $output, $return_var);
    return $return_var === 0;
}

// Function to get audio file info using FFprobe
function getAudioInfo($file_path) {
    if (!isFFmpegAvailable()) {
        return false;
    }
    
    $command = FFPROBE_PATH . ' -v quiet -print_format json -show_format -show_streams "' . $file_path . '"';
    $output = shell_exec($command);
    
    if ($output) {
        return json_decode($output, true);
    }
    
    return false;
}

// Rate limiting function
function checkRateLimit($identifier) {
    if (!isset($_SESSION['rate_limit'])) {
        $_SESSION['rate_limit'] = [];
    }
    
    $current_time = time();
    $window_start = $current_time - RATE_LIMIT_WINDOW;
    
    // Clean old entries
    $_SESSION['rate_limit'] = array_filter($_SESSION['rate_limit'], function($timestamp) use ($window_start) {
        return $timestamp > $window_start;
    });
    
    // Count requests from this identifier
    $requests = array_filter($_SESSION['rate_limit'], function($entry) use ($identifier) {
        return $entry['identifier'] === $identifier;
    });
    
    if (count($requests) >= RATE_LIMIT_MAX_REQUESTS) {
        return false; // Rate limit exceeded
    }
    
    // Add current request
    $_SESSION['rate_limit'][] = [
        'identifier' => $identifier,
        'timestamp' => $current_time
    ];
    
    return true; // Within rate limit
}
?>
