<?php
session_start();
require_once 'config.php';

echo "<h2>Debug Information</h2>";

echo "<h3>Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Available Processing IDs:</h3>";
if (isset($_SESSION['processing'])) {
    foreach ($_SESSION['processing'] as $id => $info) {
        echo "<p><strong>ID:</strong> $id</p>";
        echo "<p><strong>Status:</strong> " . $info['status'] . "</p>";
        echo "<p><strong>Original Name:</strong> " . $info['original_name'] . "</p>";
        if (isset($info['output_files'])) {
            echo "<p><strong>Output Files:</strong></p>";
            echo "<ul>";
            foreach ($info['output_files'] as $track => $path) {
                $exists = file_exists($path) ? 'EXISTS' : 'NOT FOUND';
                $size = file_exists($path) ? filesize($path) : 0;
                echo "<li>$track: $path ($exists, $size bytes)</li>";
            }
            echo "</ul>";
        }
        echo "<hr>";
    }
} else {
    echo "<p>No processing data in session</p>";
}

echo "<h3>Output Directory Contents:</h3>";
$output_dirs = glob(OUTPUT_DIR . '*', GLOB_ONLYDIR);
foreach ($output_dirs as $dir) {
    echo "<p><strong>Directory:</strong> $dir</p>";
    $files = glob($dir . '/*');
    echo "<ul>";
    foreach ($files as $file) {
        $size = filesize($file);
        echo "<li>" . basename($file) . " ($size bytes)</li>";
    }
    echo "</ul>";
}

echo "<h3>Test Download Links:</h3>";
if (isset($_SESSION['processing'])) {
    foreach ($_SESSION['processing'] as $id => $info) {
        if ($info['status'] === 'completed') {
            echo "<p><strong>Processing ID:</strong> $id</p>";
            $tracks = ['vocals', 'bass', 'drums', 'other'];
            foreach ($tracks as $track) {
                echo "<a href='download.php?id=$id&track=$track' target='_blank'>Download $track</a><br>";
            }
            echo "<a href='download.php?id=$id&all=true' target='_blank'>Download All</a><br>";
            echo "<hr>";
        }
    }
}
?>
