<?php
session_start();
require_once 'config.php';

echo "<h1>🎵 Demo Comparison: <PERSON><PERSON><PERSON><PERSON> vs Audio Asli</h1>";

// Check what's available
$has_ffmpeg = isFFmpegAvailable();
$has_spleeter = isSpleeterAvailable();

echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>📊 Status Instalasi:</h2>";
echo "<p>🔍 <strong>Spleeter (AI):</strong> " . ($has_spleeter ? "✅ Tersedia" : "❌ Tidak tersedia") . "</p>";
echo "<p>🔧 <strong>FFmpeg:</strong> " . ($has_ffmpeg ? "✅ Tersedia" : "❌ Tidak tersedia") . "</p>";
echo "<p>🎭 <strong>Simulasi:</strong> ✅ Selalu tersedia (demo only)</p>";
echo "</div>";

// Create different types of test audio
function createMusicSimulation($filename, $type = 'complex') {
    $sample_rate = 44100;
    $duration = 8; // 8 seconds
    $channels = 2;
    $bits_per_sample = 16;
    
    $data_size = $sample_rate * $duration * $channels * ($bits_per_sample / 8);
    $file_size = $data_size + 36;
    
    // WAV header
    $header = pack('V', 0x46464952); // "RIFF"
    $header .= pack('V', $file_size);
    $header .= pack('V', 0x45564157); // "WAVE"
    $header .= pack('V', 0x20746d66); // "fmt "
    $header .= pack('V', 16);
    $header .= pack('v', 1);
    $header .= pack('v', $channels);
    $header .= pack('V', $sample_rate);
    $header .= pack('V', $sample_rate * $channels * ($bits_per_sample / 8));
    $header .= pack('v', $channels * ($bits_per_sample / 8));
    $header .= pack('v', $bits_per_sample);
    $header .= pack('V', 0x61746164); // "data"
    $header .= pack('V', $data_size);
    
    $file = fopen($filename, 'wb');
    fwrite($file, $header);
    
    for ($i = 0; $i < $data_size / 4; $i++) {
        $time = $i / $sample_rate;
        $sample = 0;
        
        if ($type === 'complex') {
            // Simulate a complex music piece
            $bass = 1500 * sin(2 * M_PI * 60 * $time); // Deep bass
            $chord1 = 1000 * sin(2 * M_PI * 220 * $time); // A3
            $chord2 = 800 * sin(2 * M_PI * 277 * $time);  // C#4
            $chord3 = 600 * sin(2 * M_PI * 330 * $time);  // E4
            $melody = 2000 * sin(2 * M_PI * 440 * $time) * (1 + 0.1 * sin(2 * M_PI * 5 * $time)); // A4 with vibrato
            $drums = ($i % 11025 < 500) ? 3000 * sin(2 * M_PI * 80 * $time) : 0; // Kick drum every quarter note
            $hihat = ($i % 2756 < 100) ? 500 * (rand(-1000, 1000) / 1000) : 0; // Hi-hat noise
            
            $sample = ($bass + $chord1 + $chord2 + $chord3 + $melody + $drums + $hihat) / 7;
            
        } elseif ($type === 'vocal') {
            // Simulate vocal-like sound
            $fundamental = 1500 * sin(2 * M_PI * 200 * $time);
            $harmonic2 = 800 * sin(2 * M_PI * 400 * $time);
            $harmonic3 = 400 * sin(2 * M_PI * 600 * $time);
            $vibrato = 1 + 0.05 * sin(2 * M_PI * 6 * $time);
            
            $sample = ($fundamental + $harmonic2 + $harmonic3) * $vibrato / 3;
            
        } elseif ($type === 'instrumental') {
            // Simulate instrumental music
            $guitar = 1200 * sin(2 * M_PI * 330 * $time) * exp(-$time * 0.5);
            $piano = 1000 * sin(2 * M_PI * 440 * $time) * exp(-($time % 1) * 2);
            $strings = 800 * sin(2 * M_PI * 220 * $time);
            
            $sample = ($guitar + $piano + $strings) / 3;
        }
        
        // Clamp to 16-bit range
        $sample = max(-32768, min(32767, (int)$sample));
        
        fwrite($file, pack('v', $sample)); // Left channel
        fwrite($file, pack('v', $sample)); // Right channel
    }
    
    fclose($file);
}

// Create test files
echo "<h2>🎼 Membuat File Audio Test...</h2>";

$test_files = [
    'complex_music.wav' => 'complex',
    'vocal_simulation.wav' => 'vocal', 
    'instrumental.wav' => 'instrumental'
];

foreach ($test_files as $filename => $type) {
    createMusicSimulation($filename, $type);
    $size = filesize($filename);
    echo "<p>✅ Created: <strong>$filename</strong> ($type, " . number_format($size) . " bytes)</p>";
}

echo "<h2>🎧 Test Audio Files:</h2>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;'>";

foreach ($test_files as $filename => $type) {
    echo "<div style='background: white; padding: 15px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);'>";
    echo "<h3>🎵 " . ucfirst(str_replace('_', ' ', pathinfo($filename, PATHINFO_FILENAME))) . "</h3>";
    echo "<p><strong>Type:</strong> " . ucfirst($type) . "</p>";
    echo "<p><strong>Duration:</strong> 8 seconds</p>";
    echo "<audio controls style='width: 100%; margin: 10px 0;'>";
    echo "<source src='$filename' type='audio/wav'>";
    echo "Your browser does not support the audio element.";
    echo "</audio>";
    echo "<p><a href='$filename' download style='background: #4fc3f7; color: white; padding: 5px 10px; text-decoration: none; border-radius: 5px;'>Download</a></p>";
    echo "</div>";
}

echo "</div>";

// Test processing with current setup
echo "<h2>🧪 Test Processing dengan Setup Saat Ini:</h2>";

$processing_method = 'simulation';
if ($has_spleeter) {
    $processing_method = 'spleeter';
} elseif ($has_ffmpeg) {
    $processing_method = 'ffmpeg';
}

echo "<div style='background: " . ($processing_method === 'simulation' ? '#fff3cd' : '#d4edda') . "; padding: 15px; border-radius: 10px; margin: 10px 0;'>";
echo "<h3>📊 Metode Processing Aktif: " . strtoupper($processing_method) . "</h3>";

if ($processing_method === 'simulation') {
    echo "<p>⚠️ <strong>Mode Simulasi:</strong> Hasil akan berupa beep sounds, bukan pemisahan audio sesungguhnya.</p>";
    echo "<p>🔧 <strong>Untuk audio asli:</strong> Install Spleeter atau FFmpeg terlebih dahulu.</p>";
} elseif ($processing_method === 'ffmpeg') {
    echo "<p>✅ <strong>FFmpeg tersedia:</strong> Akan menggunakan frequency filtering untuk pemisahan basic.</p>";
} else {
    echo "<p>🎯 <strong>Spleeter tersedia:</strong> Akan menggunakan AI untuk pemisahan berkualitas tinggi!</p>";
}
echo "</div>";

// Create a test processing
echo "<h2>🚀 Test Processing:</h2>";
echo "<p>Pilih salah satu file audio di atas, lalu:</p>";
echo "<ol>";
echo "<li><a href='index.php' style='color: #4fc3f7; font-weight: bold;'>Buka halaman utama</a></li>";
echo "<li>Upload file audio yang sudah dibuat</li>";
echo "<li>Klik 'Process Audio'</li>";
echo "<li>Lihat hasilnya!</li>";
echo "</ol>";

// Show expected results
echo "<h2>📈 Hasil yang Diharapkan:</h2>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;'>";

// Simulation results
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 10px;'>";
echo "<h3>🎭 Simulasi (Saat Ini)</h3>";
echo "<ul>";
echo "<li><strong>Vocals:</strong> Beep setiap 8000 samples</li>";
echo "<li><strong>Bass:</strong> Beep setiap 16000 samples</li>";
echo "<li><strong>Drums:</strong> Beep setiap 4000 samples</li>";
echo "<li><strong>Other:</strong> Beep setiap 12000 samples</li>";
echo "</ul>";
echo "<p><em>Suara: 'Tut tut tut' dengan pola berbeda</em></p>";
echo "</div>";

// FFmpeg results
echo "<div style='background: #cce5ff; padding: 15px; border-radius: 10px;'>";
echo "<h3>🔧 FFmpeg (Jika Terinstall)</h3>";
echo "<ul>";
echo "<li><strong>Vocals:</strong> Center channel isolation</li>";
echo "<li><strong>Bass:</strong> Low-pass filter (&lt;250Hz)</li>";
echo "<li><strong>Drums:</strong> Band-pass filter (100Hz-2kHz)</li>";
echo "<li><strong>Other:</strong> High-pass filter (&gt;1kHz)</li>";
echo "</ul>";
echo "<p><em>Suara: Audio asli dengan filtering frequency</em></p>";
echo "</div>";

// Spleeter results
echo "<div style='background: #d4edda; padding: 15px; border-radius: 10px;'>";
echo "<h3>🎯 Spleeter (Jika Terinstall)</h3>";
echo "<ul>";
echo "<li><strong>Vocals:</strong> AI-separated vocals</li>";
echo "<li><strong>Bass:</strong> AI-separated bass</li>";
echo "<li><strong>Drums:</strong> AI-separated drums</li>";
echo "<li><strong>Other:</strong> AI-separated instruments</li>";
echo "</ul>";
echo "<p><em>Suara: Pemisahan berkualitas tinggi seperti studio</em></p>";
echo "</div>";

echo "</div>";

// Installation guide
echo "<h2>📚 Panduan Instalasi:</h2>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px;'>";
echo "<h3>🚀 Untuk Hasil Audio Sesungguhnya:</h3>";
echo "<pre style='background: #343a40; color: #fff; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
echo "# Install Spleeter (Recommended)\n";
echo "pip install spleeter tensorflow\n\n";
echo "# Atau install FFmpeg (Basic)\n";
echo "# Windows:\n";
echo "choco install ffmpeg\n\n";
echo "# Ubuntu:\n";
echo "sudo apt install ffmpeg\n\n";
echo "# Test instalasi:\n";
echo "spleeter --version\n";
echo "ffmpeg -version\n";
echo "</pre>";
echo "<p><a href='INSTALLATION_GUIDE.md' target='_blank' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>📖 Baca Panduan Lengkap</a></p>";
echo "</div>";

echo "<style>";
echo "body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; background: #f5f5f5; }";
echo "h1, h2, h3 { color: #333; }";
echo "audio { margin: 10px 0; }";
echo "</style>";
?>
