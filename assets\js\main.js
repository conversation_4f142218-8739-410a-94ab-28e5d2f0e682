document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('audio_file');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const uploadForm = document.getElementById('uploadForm');

    // File input change handler
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Display file information
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'block';
            
            // Validate file
            if (!validateFile(file)) {
                return;
            }
        } else {
            fileInfo.style.display = 'none';
        }
    });

    // Form submission handler
    uploadForm.addEventListener('submit', function(e) {
        const file = fileInput.files[0];
        if (!file) {
            e.preventDefault();
            showError('Please select an audio file first.');
            return;
        }
        
        if (!validateFile(file)) {
            e.preventDefault();
            return;
        }
        
        // Show loading state
        showLoading();
    });

    // File validation
    function validateFile(file) {
        const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/flac', 'audio/mp4', 'audio/ogg'];
        const allowedExtensions = ['mp3', 'wav', 'flac', 'm4a', 'ogg'];
        const maxSize = 50 * 1024 * 1024; // 50MB
        
        // Check file size
        if (file.size > maxSize) {
            showError('File size exceeds maximum limit (50MB).');
            return false;
        }
        
        // Check file extension
        const extension = file.name.split('.').pop().toLowerCase();
        if (!allowedExtensions.includes(extension)) {
            showError('Invalid file format. Supported formats: ' + allowedExtensions.join(', '));
            return false;
        }
        
        return true;
    }

    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Show error message
    function showError(message) {
        // Remove existing error messages
        const existingError = document.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        // Create new error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            ${message}
        `;
        
        // Insert before upload form
        const uploadSection = document.querySelector('.upload-section');
        uploadSection.insertBefore(errorDiv, uploadForm);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }

    // Show loading state
    function showLoading() {
        const processBtn = document.querySelector('.process-btn');
        if (processBtn) {
            processBtn.innerHTML = `
                <i class="fas fa-spinner fa-spin"></i>
                Processing...
            `;
            processBtn.disabled = true;
        }
    }

    // Volume control functionality
    const volumeControls = document.querySelectorAll('.volume-control');
    volumeControls.forEach(control => {
        control.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (icon.classList.contains('fa-volume-up')) {
                icon.classList.remove('fa-volume-up');
                icon.classList.add('fa-volume-mute');
                this.style.color = '#666';
            } else {
                icon.classList.remove('fa-volume-mute');
                icon.classList.add('fa-volume-up');
                this.style.color = '#4fc3f7';
            }
        });
    });

    // Drag and drop functionality
    const uploadForm_element = document.querySelector('.upload-form');
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadForm_element.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        uploadForm_element.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadForm_element.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        uploadForm_element.style.borderColor = 'rgba(79, 195, 247, 0.8)';
        uploadForm_element.style.background = 'rgba(255, 255, 255, 0.1)';
    }

    function unhighlight(e) {
        uploadForm_element.style.borderColor = 'rgba(79, 195, 247, 0.3)';
        uploadForm_element.style.background = 'rgba(255, 255, 255, 0.05)';
    }

    uploadForm_element.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            fileInput.files = files;
            // Trigger change event
            const event = new Event('change', { bubbles: true });
            fileInput.dispatchEvent(event);
        }
    }

    // Animate waveforms on page load
    function animateWaveforms() {
        const waveforms = document.querySelectorAll('.waveform');
        waveforms.forEach((waveform, index) => {
            setTimeout(() => {
                waveform.style.opacity = '1';
                waveform.style.transform = 'translateX(0)';
            }, index * 200);
        });
    }

    // Initialize animations
    setTimeout(animateWaveforms, 500);
});
