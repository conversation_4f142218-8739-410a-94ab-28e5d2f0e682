<?php
// Spleeter Integration for Real Audio Separation
require_once 'config.php';

class SpleeterProcessor {
    private $spleeter_path;
    private $models_path;
    
    public function __construct() {
        $this->spleeter_path = 'spleeter'; // Adjust path as needed
        $this->models_path = 'pretrained_models/'; // Spleeter models directory
    }
    
    /**
     * Check if Spleeter is available
     */
    public function isAvailable() {
        $output = [];
        $return_var = 0;
        exec($this->spleeter_path . ' --version 2>&1', $output, $return_var);
        return $return_var === 0;
    }
    
    /**
     * Separate audio using Spleeter
     */
    public function separateAudio($input_file, $output_dir, $model = '4stems-16kHz') {
        // Ensure output directory exists
        if (!file_exists($output_dir)) {
            mkdir($output_dir, 0755, true);
        }
        
        // Build Spleeter command
        $command = sprintf(
            '%s separate -p spleeter:%s -o "%s" "%s" 2>&1',
            $this->spleeter_path,
            $model,
            $output_dir,
            $input_file
        );
        
        // Execute command
        $output = [];
        $return_var = 0;
        exec($command, $output, $return_var);
        
        if ($return_var === 0) {
            return $this->organizeSpleeterOutput($input_file, $output_dir, $model);
        } else {
            throw new Exception('Spleeter processing failed: ' . implode("\n", $output));
        }
    }
    
    /**
     * Organize Spleeter output files
     */
    private function organizeSpleeterOutput($input_file, $output_dir, $model) {
        $basename = pathinfo($input_file, PATHINFO_FILENAME);
        $spleeter_output_dir = $output_dir . $basename . '/';
        
        $track_mapping = [
            '4stems-16kHz' => [
                'vocals' => 'vocals.wav',
                'bass' => 'bass.wav', 
                'drums' => 'drums.wav',
                'other' => 'other.wav'
            ],
            '2stems-16kHz' => [
                'vocals' => 'vocals.wav',
                'accompaniment' => 'accompaniment.wav'
            ],
            '5stems-16kHz' => [
                'vocals' => 'vocals.wav',
                'bass' => 'bass.wav',
                'drums' => 'drums.wav',
                'piano' => 'piano.wav',
                'other' => 'other.wav'
            ]
        ];
        
        $tracks = $track_mapping[$model] ?? $track_mapping['4stems-16kHz'];
        $result_files = [];
        
        foreach ($tracks as $track_name => $filename) {
            $source_file = $spleeter_output_dir . $filename;
            $dest_file = $output_dir . $track_name . '.wav';
            
            if (file_exists($source_file)) {
                copy($source_file, $dest_file);
                $result_files[$track_name] = $dest_file;
            }
        }
        
        // Clean up Spleeter's nested directory structure
        $this->cleanupSpleeterOutput($spleeter_output_dir);
        
        return $result_files;
    }
    
    /**
     * Clean up Spleeter's output directory
     */
    private function cleanupSpleeterOutput($dir) {
        if (is_dir($dir)) {
            $files = glob($dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
            rmdir($dir);
        }
    }
    
    /**
     * Get available models
     */
    public function getAvailableModels() {
        return [
            '2stems-16kHz' => 'Vocals / Accompaniment',
            '4stems-16kHz' => 'Vocals / Bass / Drums / Other',
            '5stems-16kHz' => 'Vocals / Bass / Drums / Piano / Other'
        ];
    }
    
    /**
     * Download and install Spleeter models
     */
    public function downloadModels($model = '4stems-16kHz') {
        $command = sprintf(
            '%s separate -p spleeter:%s --help 2>&1',
            $this->spleeter_path,
            $model
        );
        
        exec($command, $output, $return_var);
        return $return_var === 0;
    }
}

// Usage example:
function processWithSpleeter($processing_id, $input_file, $output_path) {
    $processor = new SpleeterProcessor();
    
    if (!$processor->isAvailable()) {
        throw new Exception('Spleeter is not installed or not available');
    }
    
    try {
        // Update progress
        $_SESSION['processing'][$processing_id]['progress'] = 20;
        $_SESSION['processing'][$processing_id]['message'] = 'Loading Spleeter model...';
        
        // Separate audio
        $result_files = $processor->separateAudio($input_file, $output_path, '4stems-16kHz');
        
        // Update progress
        $_SESSION['processing'][$processing_id]['progress'] = 100;
        $_SESSION['processing'][$processing_id]['message'] = 'Processing completed successfully!';
        $_SESSION['processing'][$processing_id]['status'] = 'completed';
        $_SESSION['processing'][$processing_id]['output_files'] = $result_files;
        
        return true;
        
    } catch (Exception $e) {
        $_SESSION['processing'][$processing_id]['status'] = 'error';
        $_SESSION['processing'][$processing_id]['message'] = 'Error: ' . $e->getMessage();
        return false;
    }
}

// Installation instructions
function getSpleeterInstallInstructions() {
    return [
        'step1' => 'Install Python 3.7+ and pip',
        'step2' => 'Install Spleeter: pip install spleeter',
        'step3' => 'Install FFmpeg: Required for audio processing',
        'step4' => 'Test installation: spleeter --version',
        'step5' => 'Download models: spleeter separate -p spleeter:4stems-16kHz --help'
    ];
}
?>
