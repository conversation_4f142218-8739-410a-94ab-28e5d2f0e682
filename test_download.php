<?php
// Simple test to check if download works
session_start();
require_once 'config.php';

// Get the latest processing ID from session
$latest_id = null;
if (isset($_SESSION['processing'])) {
    foreach ($_SESSION['processing'] as $id => $info) {
        if ($info['status'] === 'completed') {
            $latest_id = $id;
            break;
        }
    }
}

if (!$latest_id) {
    die('No completed processing found. Please run test_process.php first.');
}

echo "<h2>Testing Download for ID: $latest_id</h2>";

// Test individual track downloads
$tracks = ['vocals', 'bass', 'drums', 'other'];
foreach ($tracks as $track) {
    $url = "download.php?id=$latest_id&track=$track";
    echo "<p><a href='$url' download>Test Download $track</a></p>";
    
    // Check if file exists
    $file_path = OUTPUT_DIR . $latest_id . '/' . $track . '.wav';
    $exists = file_exists($file_path) ? 'EXISTS' : 'NOT FOUND';
    $size = file_exists($file_path) ? filesize($file_path) : 0;
    echo "<p>File: $file_path ($exists, $size bytes)</p>";
}

// Test ZIP download
echo "<p><a href='download.php?id=$latest_id&all=true' download>Test Download All (ZIP)</a></p>";

// Test with curl to see response
echo "<h3>Testing with Server Request:</h3>";
$test_url = "http://localhost:8000/download.php?id=$latest_id&track=vocals";
echo "<p>Testing URL: $test_url</p>";

// Use file_get_contents to test
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'User-Agent: Test Script'
    ]
]);

$response = @file_get_contents($test_url, false, $context);
if ($response === false) {
    echo "<p style='color: red;'>Error: Could not fetch download URL</p>";
    $error = error_get_last();
    echo "<p>Error details: " . $error['message'] . "</p>";
} else {
    echo "<p style='color: green;'>Success: Download URL responded with " . strlen($response) . " bytes</p>";
}

// Show response headers
if (isset($http_response_header)) {
    echo "<h4>Response Headers:</h4>";
    echo "<pre>";
    foreach ($http_response_header as $header) {
        echo htmlspecialchars($header) . "\n";
    }
    echo "</pre>";
}
?>
