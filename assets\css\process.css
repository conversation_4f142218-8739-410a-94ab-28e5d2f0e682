/* Processing Page Specific Styles */

.processing-section {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.processing-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.processing-subtitle {
    font-size: 16px;
    color: #b0bec5;
    margin-bottom: 40px;
}

/* File Info Card */
.file-info-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    gap: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.file-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.file-details {
    flex: 1;
    text-align: left;
}

.file-details h3 {
    color: #ffffff;
    margin-bottom: 5px;
    font-size: 18px;
}

.file-details p {
    color: #b0bec5;
    font-size: 14px;
}

/* Progress Section */
.progress-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-bar-container {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 20px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4fc3f7, #29b6f6);
    border-radius: 4px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-percentage {
    font-size: 18px;
    font-weight: 700;
    color: #4fc3f7;
}

.progress-message {
    color: #b0bec5;
    font-size: 14px;
}

/* Status Section */
.status-section {
    margin-bottom: 40px;
}

.status-item {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 25px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 16px;
}

.status-item.waiting {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-item.processing {
    background: rgba(79, 195, 247, 0.1);
    color: #4fc3f7;
    border: 1px solid rgba(79, 195, 247, 0.3);
}

.status-item.completed {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

/* Results Section */
.results-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 40px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.results-section h2 {
    color: #4fc3f7;
    margin-bottom: 30px;
    font-size: 24px;
}

.tracks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.track-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.track-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.track-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    margin: 0 auto 15px;
}

.track-icon.vocals {
    background: linear-gradient(45deg, #9c27b0, #e91e63);
}

.track-icon.bass {
    background: linear-gradient(45deg, #ff9800, #ffc107);
}

.track-icon.drums {
    background: linear-gradient(45deg, #f44336, #ff5722);
}

.track-icon.other {
    background: linear-gradient(45deg, #4caf50, #8bc34a);
}

.track-card h3 {
    color: #ffffff;
    margin-bottom: 20px;
    font-size: 16px;
}

.track-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.play-btn, .download-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.play-btn {
    background: linear-gradient(45deg, #4fc3f7, #29b6f6);
    color: white;
}

.play-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(79, 195, 247, 0.4);
}

.download-btn {
    background: linear-gradient(45deg, #4caf50, #8bc34a);
    color: white;
}

.download-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

/* Download All Section */
.download-all-section {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 30px;
}

.download-all-btn {
    background: linear-gradient(45deg, #4caf50, #8bc34a);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.download-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .processing-title {
        font-size: 28px;
    }
    
    .file-info-card {
        flex-direction: column;
        text-align: center;
    }
    
    .file-details {
        text-align: center;
    }
    
    .tracks-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .progress-info {
        flex-direction: column;
        gap: 10px;
    }
}
