# Splitter AI - PHP Native Implementation

A PHP-based web application that mimics the functionality of Splitter AI for separating music into individual tracks (vocals, bass, drums, and other instruments) using AI-powered algorithms.

## Features

- **Modern Web Interface**: Dark theme UI similar to the original Splitter AI
- **File Upload**: Drag & drop or browse to upload audio files
- **Multiple Format Support**: MP3, WAV, FLAC, M4A, OGG, AAC
- **Real-time Progress Tracking**: AJAX-based progress monitoring
- **Audio Separation**: Separates music into 4 tracks (vocals, bass, drums, other)
- **Audio Player**: Built-in player for each separated track
- **Download System**: Individual track downloads or ZIP with all tracks
- **Responsive Design**: Works on desktop and mobile devices
- **Session Management**: Secure file handling with session-based tracking

## Requirements

- PHP 7.4 or higher
- Web server (Apache/Nginx)
- FFmpeg (for audio processing)
- Spleeter or similar AI separation tool (optional)
- ZipArchive PHP extension

## Installation

1. **Clone or download** this project to your web server directory
2. **Set permissions** for upload and output directories:
   ```bash
   chmod 755 uploads/
   chmod 755 output/
   chmod 755 temp/
   chmod 755 logs/
   ```

3. **Install FFmpeg** (if not already installed):
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install ffmpeg
   
   # Windows (using Chocolatey)
   choco install ffmpeg
   
   # macOS (using Homebrew)
   brew install ffmpeg
   ```

4. **Configure the application**:
   - Edit `config.php` to match your server settings
   - Update `APP_URL` to your domain
   - Configure FFmpeg paths if needed

5. **Optional: Install Spleeter** for actual AI separation:
   ```bash
   pip install spleeter
   ```

## File Structure

```
spliter/
├── index.php              # Main upload page
├── process.php            # Processing page with progress tracking
├── download.php           # Download handler for tracks
├── config.php             # Configuration settings
├── README.md              # This file
├── assets/
│   ├── css/
│   │   ├── style.css      # Main stylesheet
│   │   └── process.css    # Processing page styles
│   └── js/
│       ├── main.js        # Main page JavaScript
│       └── process.js     # Processing page JavaScript
├── uploads/               # Uploaded audio files
├── output/                # Separated track outputs
├── temp/                  # Temporary processing files
└── logs/                  # Error logs
```

## Configuration

### Basic Settings (config.php)

- `MAX_FILE_SIZE`: Maximum upload file size (default: 50MB)
- `ALLOWED_EXTENSIONS`: Supported audio formats
- `PROCESSING_TIMEOUT`: Maximum processing time
- `CLEANUP_AGE`: How long to keep files before cleanup

### AI Processing Integration

The current implementation includes a simulation of the separation process. To integrate with actual AI separation:

1. **Using Spleeter**:
   ```php
   // In process.php, replace simulateProcessing() with:
   function processSpleeter($input_file, $output_path) {
       $command = "spleeter separate -p spleeter:2stems-16kHz -o " . $output_path . " " . $input_file;
       exec($command, $output, $return_var);
       return $return_var === 0;
   }
   ```

2. **Using External API** (LALAL.AI, etc.):
   ```php
   function processWithAPI($input_file) {
       // Implement API calls to your chosen service
       // Return separated tracks
   }
   ```

## Usage

1. **Upload Audio File**:
   - Visit the main page
   - Click "Browse my files" or drag & drop an audio file
   - Supported formats: MP3, WAV, FLAC, M4A, OGG, AAC
   - Maximum file size: 50MB

2. **Processing**:
   - Click "Process Audio" to start separation
   - Monitor real-time progress
   - Processing typically takes 1-3 minutes depending on file size

3. **Download Results**:
   - Play individual tracks using built-in player
   - Download individual tracks or all tracks as ZIP
   - Files are automatically cleaned up after 24 hours

## Customization

### Styling
- Modify `assets/css/style.css` for main page styling
- Modify `assets/css/process.css` for processing page styling
- Colors, fonts, and layout can be easily customized

### Processing Logic
- Edit `process.php` to integrate with your preferred AI separation service
- Modify track names and processing steps as needed
- Add additional audio formats by updating configuration

### Security
- Enable CSRF protection in production
- Configure rate limiting
- Set up proper error logging
- Use HTTPS in production

## API Integration Examples

### Spleeter Integration
```php
function separateWithSpleeter($input_file, $output_dir) {
    $command = sprintf(
        'spleeter separate -p spleeter:4stems-16kHz -o "%s" "%s"',
        $output_dir,
        $input_file
    );
    
    exec($command, $output, $return_var);
    return $return_var === 0;
}
```

### LALAL.AI API Integration
```php
function separateWithLALAL($input_file) {
    $api_key = 'your-api-key';
    $url = 'https://www.lalal.ai/api/upload/';
    
    // Implementation depends on API documentation
    // Return separated tracks
}
```

## Troubleshooting

### Common Issues

1. **Upload fails**: Check file permissions and PHP upload limits
2. **Processing stuck**: Verify FFmpeg installation and paths
3. **Downloads not working**: Check output directory permissions
4. **Styling issues**: Clear browser cache and check CSS file paths

### Error Logs
Check `logs/error.log` for detailed error information.

### PHP Configuration
Ensure these PHP settings for large file uploads:
```ini
upload_max_filesize = 50M
post_max_size = 50M
max_execution_time = 300
memory_limit = 256M
```

## License

This project is for educational purposes. Please ensure you have proper licensing for any AI separation tools you integrate.

## Contributing

Feel free to submit issues and enhancement requests!

## Disclaimer

This is a demonstration implementation. For production use:
- Implement proper AI separation (Spleeter, API services)
- Add user authentication
- Implement database storage
- Add comprehensive error handling
- Use background job processing
- Implement proper security measures
