<?php
session_start();
require_once 'config.php';

echo "<h1>Real Audio Processing Test</h1>";

// Check available processing methods
echo "<h2>Available Processing Methods:</h2>";

// Check Spleeter
if (isSpleeterAvailable()) {
    echo "<p>✅ <strong>Spleeter</strong> - AI-based separation (BEST QUALITY)</p>";
    $processing_method = 'spleeter';
} elseif (isFFmpegAvailable()) {
    echo "<p>⚠️ <strong>FFmpeg</strong> - Basic frequency filtering (MEDIUM QUALITY)</p>";
    echo "<p>❌ Spleeter - Not available (install with: pip install spleeter)</p>";
    $processing_method = 'ffmpeg';
} else {
    echo "<p>❌ Spleeter - Not available</p>";
    echo "<p>❌ FFmpeg - Not available</p>";
    echo "<p>⚠️ <strong>Simulation</strong> - Demo only (NO REAL SEPARATION)</p>";
    $processing_method = 'simulation';
}

echo "<h2>Installation Instructions:</h2>";

echo "<h3>For Spleeter (Recommended):</h3>";
echo "<pre>";
echo "1. Install Python 3.7+\n";
echo "2. pip install spleeter\n";
echo "3. pip install tensorflow\n";
echo "4. Test: spleeter --version\n";
echo "</pre>";

echo "<h3>For FFmpeg (Basic):</h3>";
echo "<pre>";
echo "Windows: choco install ffmpeg\n";
echo "Ubuntu: sudo apt install ffmpeg\n";
echo "macOS: brew install ffmpeg\n";
echo "Test: ffmpeg -version\n";
echo "</pre>";

// Create a more realistic test audio file
function createRealisticTestAudio($filename) {
    $sample_rate = 44100;
    $duration = 10; // 10 seconds
    $channels = 2;
    $bits_per_sample = 16;
    
    $data_size = $sample_rate * $duration * $channels * ($bits_per_sample / 8);
    $file_size = $data_size + 36;
    
    // WAV header
    $header = pack('V', 0x46464952); // "RIFF"
    $header .= pack('V', $file_size);
    $header .= pack('V', 0x45564157); // "WAVE"
    $header .= pack('V', 0x20746d66); // "fmt "
    $header .= pack('V', 16);
    $header .= pack('v', 1);
    $header .= pack('v', $channels);
    $header .= pack('V', $sample_rate);
    $header .= pack('V', $sample_rate * $channels * ($bits_per_sample / 8));
    $header .= pack('v', $channels * ($bits_per_sample / 8));
    $header .= pack('v', $bits_per_sample);
    $header .= pack('V', 0x61746164); // "data"
    $header .= pack('V', $data_size);
    
    $file = fopen($filename, 'wb');
    fwrite($file, $header);
    
    // Generate more complex audio (multiple instruments simulation)
    for ($i = 0; $i < $data_size / 4; $i++) {
        $time = $i / $sample_rate;
        
        // Simulate multiple instruments
        $bass = 2000 * sin(2 * M_PI * 80 * $time); // Bass line
        $melody = 3000 * sin(2 * M_PI * 440 * $time) * sin(2 * M_PI * 2 * $time); // Melody with vibrato
        $harmony = 1500 * sin(2 * M_PI * 660 * $time); // Harmony
        $drums = ($i % 22050 < 1000) ? 4000 * sin(2 * M_PI * 200 * $time) : 0; // Drum hits
        
        $sample = (int)(($bass + $melody + $harmony + $drums) / 4);
        
        // Clamp to 16-bit range
        $sample = max(-32768, min(32767, $sample));
        
        fwrite($file, pack('v', $sample)); // Left channel
        fwrite($file, pack('v', $sample)); // Right channel
    }
    
    fclose($file);
}

// Create test audio
$test_file = 'realistic_test_audio.wav';
createRealisticTestAudio($test_file);
$file_size = filesize($test_file);

echo "<h2>Test Audio Created:</h2>";
echo "<p><strong>File:</strong> $test_file</p>";
echo "<p><strong>Size:</strong> " . number_format($file_size) . " bytes</p>";
echo "<p><strong>Duration:</strong> 10 seconds</p>";
echo "<p><strong>Content:</strong> Multi-instrument simulation (bass, melody, harmony, drums)</p>";

echo "<p><a href='$test_file' download>Download Test Audio</a></p>";

// Test processing with current method
echo "<h2>Test Processing:</h2>";
echo "<p><strong>Current Method:</strong> " . strtoupper($processing_method) . "</p>";

if ($processing_method === 'simulation') {
    echo "<p style='color: orange;'>⚠️ <strong>Warning:</strong> This will only produce beep sounds, not real separation!</p>";
    echo "<p>To get real audio separation, install Spleeter or FFmpeg first.</p>";
} else {
    echo "<p style='color: green;'>✅ This will produce actual separated audio tracks!</p>";
}

echo "<p><a href='index.php' class='btn'>Go to Main Page to Upload & Process</a></p>";

// Show example of what real separation would produce
echo "<h2>Expected Results:</h2>";
echo "<div style='background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

if ($processing_method === 'spleeter') {
    echo "<h3>Spleeter (AI-based):</h3>";
    echo "<ul>";
    echo "<li><strong>Vocals:</strong> Clean isolated vocals</li>";
    echo "<li><strong>Bass:</strong> Bass guitar and low-frequency instruments</li>";
    echo "<li><strong>Drums:</strong> Drum kit and percussion</li>";
    echo "<li><strong>Other:</strong> All other instruments (guitar, piano, etc.)</li>";
    echo "</ul>";
    echo "<p><em>Quality: Excellent - Uses machine learning trained on thousands of songs</em></p>";
} elseif ($processing_method === 'ffmpeg') {
    echo "<h3>FFmpeg (Frequency-based):</h3>";
    echo "<ul>";
    echo "<li><strong>Vocals:</strong> Center channel isolation (works best with stereo recordings)</li>";
    echo "<li><strong>Bass:</strong> Low-pass filtered audio (&lt;250Hz)</li>";
    echo "<li><strong>Drums:</strong> Band-pass filtered audio (100Hz-2kHz)</li>";
    echo "<li><strong>Other:</strong> High-pass filtered audio (&gt;1kHz)</li>";
    echo "</ul>";
    echo "<p><em>Quality: Basic - Simple frequency filtering, not perfect separation</em></p>";
} else {
    echo "<h3>Simulation (Demo only):</h3>";
    echo "<ul>";
    echo "<li><strong>Vocals:</strong> Beep pattern every 8000 samples</li>";
    echo "<li><strong>Bass:</strong> Beep pattern every 16000 samples</li>";
    echo "<li><strong>Drums:</strong> Beep pattern every 4000 samples</li>";
    echo "<li><strong>Other:</strong> Beep pattern every 12000 samples</li>";
    echo "</ul>";
    echo "<p><em>Quality: None - Just for demonstration purposes</em></p>";
}

echo "</div>";

echo "<style>";
echo ".btn { display: inline-block; background: #4fc3f7; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0; }";
echo ".btn:hover { background: #29b6f6; }";
echo "</style>";
?>
