<?php
session_start();
require_once 'config.php';

// Enable error reporting for debugging
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Check if processing ID is provided
if (!isset($_GET['id'])) {
    http_response_code(400);
    die('Processing ID is required');
}

$processing_id = $_GET['id'];

// Check if processing info exists in session
if (!isset($_SESSION['processing'][$processing_id])) {
    http_response_code(404);
    die('Processing session not found');
}

$processing_info = $_SESSION['processing'][$processing_id];

// Check if processing is completed
if ($processing_info['status'] !== 'completed') {
    http_response_code(400);
    die('Processing not completed yet');
}

// Handle download all tracks as ZIP
if (isset($_GET['all']) && $_GET['all'] === 'true') {
    downloadAllTracks($processing_id, $processing_info);
    exit;
}

// Handle individual track download
if (isset($_GET['track'])) {
    $track = $_GET['track'];
    downloadSingleTrack($processing_id, $track, $processing_info);
    exit;
}

http_response_code(400);
die('Invalid download request');

function downloadSingleTrack($processing_id, $track, $processing_info) {
    $allowed_tracks = ['vocals', 'bass', 'drums', 'other'];

    if (!in_array($track, $allowed_tracks)) {
        http_response_code(400);
        die('Invalid track name');
    }

    $output_path = OUTPUT_DIR . $processing_id . '/';
    $file_path = $output_path . $track . '.wav';

    // Debug information
    if (DEBUG_MODE) {
        error_log("Download attempt - Processing ID: $processing_id, Track: $track");
        error_log("File path: $file_path");
        error_log("File exists: " . (file_exists($file_path) ? 'Yes' : 'No'));
        if (file_exists($file_path)) {
            error_log("File size: " . filesize($file_path));
        }
    }

    if (!file_exists($file_path)) {
        http_response_code(404);
        die('Track file not found at: ' . $file_path);
    }
    
    // Get original filename without extension
    $original_name = pathinfo($processing_info['original_name'], PATHINFO_FILENAME);
    $download_name = $original_name . '_' . $track . '.wav';
    
    // Set headers for file download
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $download_name . '"');
    header('Content-Length: ' . filesize($file_path));
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: 0');
    header('Pragma: public');

    // Clear any previous output
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Output file content
    readfile($file_path);
    exit;
}

function downloadAllTracks($processing_id, $processing_info) {
    $output_path = OUTPUT_DIR . $processing_id . '/';
    $tracks = ['vocals', 'bass', 'drums', 'other'];
    
    // Check if all track files exist
    foreach ($tracks as $track) {
        $file_path = $output_path . $track . '.wav';
        if (!file_exists($file_path)) {
            http_response_code(404);
            die('Some track files are missing');
        }
    }
    
    // Create ZIP file
    $zip = new ZipArchive();
    $zip_filename = tempnam(sys_get_temp_dir(), 'tracks_') . '.zip';
    
    if ($zip->open($zip_filename, ZipArchive::CREATE) !== TRUE) {
        http_response_code(500);
        die('Cannot create ZIP file');
    }
    
    // Get original filename without extension
    $original_name = pathinfo($processing_info['original_name'], PATHINFO_FILENAME);
    
    // Add each track to ZIP
    foreach ($tracks as $track) {
        $file_path = $output_path . $track . '.wav';
        $zip_entry_name = $original_name . '_' . $track . '.wav';
        $zip->addFile($file_path, $zip_entry_name);
    }
    
    // Add a readme file
    $readme_content = "Separated Audio Tracks\n";
    $readme_content .= "======================\n\n";
    $readme_content .= "Original file: " . $processing_info['original_name'] . "\n";
    $readme_content .= "Processed on: " . date('Y-m-d H:i:s', $processing_info['created_at']) . "\n\n";
    $readme_content .= "Tracks included:\n";
    $readme_content .= "- " . $original_name . "_vocals.wav (Vocal track)\n";
    $readme_content .= "- " . $original_name . "_bass.wav (Bass track)\n";
    $readme_content .= "- " . $original_name . "_drums.wav (Drums track)\n";
    $readme_content .= "- " . $original_name . "_other.wav (Other instruments)\n\n";
    $readme_content .= "Generated by Splitter AI\n";
    
    $zip->addFromString('README.txt', $readme_content);
    
    $zip->close();
    
    // Set headers for ZIP download
    $download_name = $original_name . '_separated_tracks.zip';
    header('Content-Type: application/zip');
    header('Content-Disposition: attachment; filename="' . $download_name . '"');
    header('Content-Length: ' . filesize($zip_filename));
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: 0');
    header('Pragma: public');

    // Clear any previous output
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Output ZIP file
    readfile($zip_filename);

    // Clean up temporary ZIP file
    unlink($zip_filename);
    exit;
}

// Function to clean up old processing files (call this periodically)
function cleanupOldFiles() {
    $cleanup_age = 24 * 60 * 60; // 24 hours
    $current_time = time();
    
    // Clean up upload directory
    $upload_files = glob(UPLOAD_DIR . '*');
    foreach ($upload_files as $file) {
        if (is_file($file) && ($current_time - filemtime($file)) > $cleanup_age) {
            unlink($file);
        }
    }
    
    // Clean up output directory
    $output_dirs = glob(OUTPUT_DIR . '*', GLOB_ONLYDIR);
    foreach ($output_dirs as $dir) {
        if (($current_time - filemtime($dir)) > $cleanup_age) {
            // Remove all files in directory
            $files = glob($dir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
            // Remove directory
            rmdir($dir);
        }
    }
    
    // Clean up session data
    if (isset($_SESSION['processing'])) {
        foreach ($_SESSION['processing'] as $id => $info) {
            if (($current_time - $info['created_at']) > $cleanup_age) {
                unset($_SESSION['processing'][$id]);
            }
        }
    }
}

// Uncomment the line below to enable automatic cleanup (or call it via cron job)
// cleanupOldFiles();
?>
