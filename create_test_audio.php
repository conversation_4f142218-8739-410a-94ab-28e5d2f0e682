<?php
// Create a simple test audio file for upload testing

function createTestAudioFile($filename) {
    $sample_rate = 44100;
    $duration = 5; // 5 seconds
    $channels = 2; // stereo
    $bits_per_sample = 16;
    
    $data_size = $sample_rate * $duration * $channels * ($bits_per_sample / 8);
    $file_size = $data_size + 36;
    
    // WAV header
    $header = pack('V', 0x46464952); // "RIFF"
    $header .= pack('V', $file_size);
    $header .= pack('V', 0x45564157); // "WAVE"
    $header .= pack('V', 0x20746d66); // "fmt "
    $header .= pack('V', 16); // PCM header size
    $header .= pack('v', 1); // PCM format
    $header .= pack('v', $channels);
    $header .= pack('V', $sample_rate);
    $header .= pack('V', $sample_rate * $channels * ($bits_per_sample / 8));
    $header .= pack('v', $channels * ($bits_per_sample / 8));
    $header .= pack('v', $bits_per_sample);
    $header .= pack('V', 0x61746164); // "data"
    $header .= pack('V', $data_size);
    
    // Create file with header and some dummy audio data
    $file = fopen($filename, 'wb');
    fwrite($file, $header);
    
    // Generate some dummy audio data (simple sine wave)
    for ($i = 0; $i < $data_size / 4; $i++) {
        $time = $i / $sample_rate;
        $frequency = 440; // A note
        $amplitude = 5000;
        $sample = (int)($amplitude * sin(2 * M_PI * $frequency * $time));
        
        // Write stereo sample (left and right channels)
        fwrite($file, pack('v', $sample)); // Left channel
        fwrite($file, pack('v', $sample)); // Right channel
    }
    
    fclose($file);
    return filesize($filename);
}

$test_file = 'test_audio.wav';
$size = createTestAudioFile($test_file);

echo "<h2>Test Audio File Created</h2>";
echo "<p><strong>Filename:</strong> $test_file</p>";
echo "<p><strong>Size:</strong> " . number_format($size) . " bytes</p>";
echo "<p><a href='$test_file' download>Download Test Audio File</a></p>";
echo "<p><a href='index.php'>Go to Main Page to Upload</a></p>";
?>
